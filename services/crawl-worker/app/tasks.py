"""Celery tasks for web crawling."""

import asyncio
import logging
import time
from typing import Dict, Any, Optional
from urllib.parse import urlparse
from celery import Celery
from celery.exceptions import Retry
from crawl4ai import AsyncWebCrawler
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy
from sqlalchemy import select, update
from .config import settings
from .database import get_db_session
from .models import Link, Page, SiteConfig, LinkStatus, CrawlResult
from .utils import (
    calculate_content_hash,
    extract_domain,
    extract_links_from_content,
    create_extraction_strategy,
    validate_url,
    is_allowed_by_robots
)

# Configure logging
logging.basicConfig(level=getattr(logging, settings.log_level.upper()))
logger = logging.getLogger(__name__)

# Initialize Celery app
app = Celery(
    "crawl_worker",
    broker=settings.broker_url,
    backend=settings.result_backend
)

# Celery configuration
app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,
    task_time_limit=300,  # 5 minutes
    task_soft_time_limit=240,  # 4 minutes
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    worker_disable_rate_limits=False,
    task_default_retry_delay=60,
    task_max_retries=settings.max_retries,
    broker_connection_retry_on_startup=True,  # Fix deprecation warning
    worker_send_task_events=True,  # Enable task events for Flower monitoring
    task_send_sent_event=True,  # Send task sent events
    task_routes={
        "crawl_worker.tasks.crawl_page": {"queue": "crawl"},
        "crawl_worker.tasks.extract_links": {"queue": "extract"},
        "crawl_worker.tasks.health_check": {"queue": "health"},
    }
)


@app.task(bind=True, autoretry_for=(Exception,), retry_kwargs={"max_retries": 3, "countdown": 60})
def crawl_page(self, url: str, config_override: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Main crawling task with retry logic.

    Args:
        url: URL to crawl
        config_override: Optional configuration override

    Returns:
        Dict containing crawl results
    """
    return asyncio.run(_crawl_page_async(self, url, config_override))


async def _crawl_page_async(task_self, url: str, config_override: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """Async implementation of crawl_page task."""
    start_time = time.time()
    task_id = task_self.request.id

    logger.info(f"Starting crawl task {task_id} for URL: {url}")

    # Validate URL
    if not validate_url(url):
        error_msg = f"Invalid URL format: {url}"
        logger.error(error_msg)
        await _update_link_status(url, LinkStatus.FAILED, error_msg)
        raise ValueError(error_msg)

    # Check robots.txt if enabled
    if settings.respect_robots_txt and not is_allowed_by_robots(url, settings.user_agent):
        error_msg = f"URL blocked by robots.txt: {url}"
        logger.warning(error_msg)
        await _update_link_status(url, LinkStatus.FAILED, error_msg)
        return {"error": error_msg, "status": "blocked"}
    
    try:
        # Update link status to crawling
        await _update_link_status(url, LinkStatus.CRAWLING)
        
        # Get site configuration
        domain = extract_domain(url)
        site_config = await _get_site_config(domain)
        
        # Use config override if provided
        if config_override:
            site_config.update(config_override)
        
        # Create extraction strategy
        extraction_strategy = create_extraction_strategy(site_config)
        
        # Perform crawling
        async with AsyncWebCrawler(
            headless=True,
            verbose=settings.debug,
            user_agent=settings.user_agent
        ) as crawler:
            
            result = await crawler.arun(
                url=url,
                extraction_strategy=extraction_strategy,
                bypass_cache=True,
                timeout=settings.request_timeout
            )
            
            if not result.success:
                error_msg = f"Crawl failed: {result.error_message}"
                logger.error(f"Task {task_id} failed: {error_msg}")
                await _update_link_status(url, LinkStatus.FAILED, error_msg)
                raise Exception(error_msg)
            
            # Process crawl result
            content_hash = calculate_content_hash(result.html)
            
            # Check for duplicate content
            if await _is_duplicate_content(content_hash):
                logger.info(f"Duplicate content detected for {url}")
                await _update_link_status(url, LinkStatus.COMPLETED)
                return {"status": "duplicate", "content_hash": content_hash}
            
            # Extract structured data
            extracted_data = {}
            if result.extracted_content:
                try:
                    import json
                    extracted_data = json.loads(result.extracted_content)
                except Exception as e:
                    logger.warning(f"Failed to parse extracted content: {e}")
                    extracted_data = {"raw": result.extracted_content}
            
            # Extract links for further crawling
            found_links = extract_links_from_content(result.html, url)
            
            # Save page data
            page_data = {
                "url": url,
                "title": extracted_data.get("title", ""),
                "content": {
                    "html": result.html,
                    "markdown": result.markdown,
                    "extracted": extracted_data
                },
                "extracted_data": extracted_data,
                "content_hash": content_hash,
                "status_code": 200  # Assuming success if we got here
            }
            
            await _save_page_data(page_data)
            
            # Queue discovered links
            queued_links = await _queue_discovered_links(found_links, domain)
            
            # Update link status to completed
            await _update_link_status(url, LinkStatus.COMPLETED)
            
            processing_time = time.time() - start_time
            
            crawl_result = CrawlResult(
                url=url,
                status_code=200,
                title=extracted_data.get("title", ""),
                content=page_data["content"],
                extracted_data=extracted_data,
                content_hash=content_hash,
                links_found=len(found_links),
                processing_time=processing_time
            )
            
            logger.info(f"Task {task_id} completed successfully in {processing_time:.2f}s")
            return crawl_result.model_dump()
            
    except Exception as e:
        error_msg = str(e)
        logger.error(f"Task {task_id} failed: {error_msg}")
        
        # Update retry count and status
        retry_count = task_self.request.retries
        if retry_count >= settings.max_retries:
            await _update_link_status(url, LinkStatus.FAILED, error_msg)
        else:
            await _increment_retry_count(url)

        # Re-raise for Celery retry mechanism
        raise task_self.retry(countdown=settings.retry_delay * (2 ** retry_count))


@app.task(bind=True)
def extract_links(self, url: str, max_depth: int = 2) -> Dict[str, Any]:
    """
    Recursive link discovery task.

    Args:
        url: Starting URL
        max_depth: Maximum crawl depth

    Returns:
        Dict containing extraction results
    """
    return asyncio.run(_extract_links_async(self, url, max_depth))


async def _extract_links_async(task_self, url: str, max_depth: int = 2) -> Dict[str, Any]:
    """Async implementation of extract_links task."""
    task_id = task_self.request.id
    logger.info(f"Starting link extraction task {task_id} for URL: {url}")

    try:
        domain = extract_domain(url)

        # Simple crawl to get page content
        async with AsyncWebCrawler(headless=True, verbose=settings.debug) as crawler:
            result = await crawler.arun(url=url, bypass_cache=True)

            if not result.success:
                logger.error(f"Failed to extract links from {url}: {result.error_message}")
                return {"error": result.error_message, "links_found": 0}

            # Extract links
            found_links = extract_links_from_content(result.html, url)

            # Queue discovered links
            queued_count = await _queue_discovered_links(found_links, domain)

            logger.info(f"Link extraction task {task_id} completed: {len(found_links)} links found, {queued_count} queued")

            return {
                "url": url,
                "links_found": len(found_links),
                "links_queued": queued_count,
                "status": "completed"
            }

    except Exception as e:
        error_msg = str(e)
        logger.error(f"Link extraction task {task_id} failed: {error_msg}")
        return {"error": error_msg, "status": "failed"}


@app.task
def health_check() -> Dict[str, Any]:
    """Worker health monitoring task."""
    return {
        "status": "healthy",
        "worker_id": app.control.inspect().active(),
        "timestamp": time.time()
    }


# Helper functions
async def _get_site_config(domain: str) -> Dict[str, Any]:
    """Get site-specific configuration."""
    try:
        async with get_db_session() as db:
            result = await db.execute(
                select(SiteConfig).where(SiteConfig.domain == domain)
            )
            site_config = result.scalar_one_or_none()
            
            if site_config:
                return site_config.config
            else:
                return settings.default_extraction_config
                
    except Exception as e:
        logger.warning(f"Failed to get site config for {domain}: {e}")
        return settings.default_extraction_config


async def _update_link_status(url: str, status: LinkStatus, error_message: str = None):
    """Update link status in database."""
    try:
        async with get_db_session() as db:
            update_data = {"status": status, "updated_at": "NOW()"}
            if error_message:
                metadata_update = {"error_message": error_message}
                update_data["link_metadata"] = metadata_update
            
            await db.execute(
                update(Link).where(Link.url == url).values(**update_data)
            )
            await db.commit()
            
    except Exception as e:
        logger.error(f"Failed to update link status for {url}: {e}")


async def _increment_retry_count(url: str):
    """Increment retry count for a link."""
    try:
        async with get_db_session() as db:
            await db.execute(
                update(Link)
                .where(Link.url == url)
                .values(retry_count=Link.retry_count + 1)
            )
            await db.commit()
            
    except Exception as e:
        logger.error(f"Failed to increment retry count for {url}: {e}")


async def _is_duplicate_content(content_hash: str) -> bool:
    """Check if content hash already exists."""
    try:
        async with get_db_session() as db:
            result = await db.execute(
                select(Page).where(Page.content_hash == content_hash)
            )
            return result.scalar_one_or_none() is not None
            
    except Exception as e:
        logger.error(f"Failed to check duplicate content: {e}")
        return False


async def _save_page_data(page_data: Dict[str, Any]):
    """Save crawled page data to database."""
    try:
        async with get_db_session() as db:
            page = Page(**page_data)
            db.add(page)
            await db.commit()
            
    except Exception as e:
        logger.error(f"Failed to save page data: {e}")


async def _queue_discovered_links(links: list, domain: str) -> int:
    """Queue discovered links for crawling."""
    try:
        queued_count = 0
        async with get_db_session() as db:
            for link_url in links:
                # Check if link already exists
                result = await db.execute(
                    select(Link).where(Link.url == link_url)
                )
                if not result.scalar_one_or_none():
                    new_link = Link(
                        url=link_url,
                        domain=domain,
                        status=LinkStatus.PENDING
                    )
                    db.add(new_link)
                    queued_count += 1
            
            await db.commit()
            
        return queued_count
        
    except Exception as e:
        logger.error(f"Failed to queue discovered links: {e}")
        return 0
