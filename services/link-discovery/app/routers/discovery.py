"""Link discovery and site configuration endpoints."""

import logging
import uuid
from typing import List, Optional
from urllib.parse import urlparse
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_
from sqlalchemy.orm import selectinload
from celery import Celery
from ..database import get_db
from ..models import (
    SiteConfig, Link, LinkStatus,
    SiteConfigCreate, SiteConfigResponse,
    LinkCreate, LinkResponse,
    DiscoveryRequest, DiscoveryResponse
)
from ..config import settings

logger = logging.getLogger(__name__)
router = APIRouter()

# Initialize Celery client
celery_app = Celery(
    "crawl_worker",
    broker=settings.broker_url,
    backend=settings.result_backend
)

# Configure Celery to fix deprecation warnings
celery_app.conf.update(
    broker_connection_retry_on_startup=True
)


@router.post("/discover", response_model=DiscoveryResponse)
async def discover_links(
    request: DiscoveryRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Discover links from seed URLs and enqueue crawl tasks.
    
    Args:
        request: Discovery request with URLs and configuration
        db: Database session
        
    Returns:
        DiscoveryResponse: Summary of discovery operation
    """
    try:
        correlation_id = str(uuid.uuid4())
        logger.info(f"Starting link discovery {correlation_id} for {len(request.urls)} URLs")
        
        discovered_count = 0
        queued_tasks = 0
        
        for url in request.urls:
            url_str = str(url)
            domain = urlparse(url_str).netloc
            
            # Check if link already exists
            existing_link = await db.execute(
                select(Link).where(Link.url == url_str)
            )
            if existing_link.scalar_one_or_none():
                logger.debug(f"Link already exists: {url_str}")
                continue
            
            # Create new link record
            new_link = Link(
                url=url_str,
                domain=domain,
                link_metadata={"correlation_id": correlation_id},
                priority=request.priority,
                status=LinkStatus.PENDING
            )
            db.add(new_link)
            discovered_count += 1
            
            # Queue crawl task
            try:
                task = celery_app.send_task(
                    "crawl_worker.tasks.crawl_page",
                    args=[url_str],
                    kwargs={"config_override": request.site_config},
                    priority=request.priority
                )
                queued_tasks += 1
                logger.debug(f"Queued crawl task {task.id} for {url_str}")
            except Exception as e:
                logger.error(f"Failed to queue task for {url_str}: {e}")
        
        await db.commit()
        
        message = f"Discovered {discovered_count} new links, queued {queued_tasks} crawl tasks"
        logger.info(f"Discovery {correlation_id} completed: {message}")
        
        return DiscoveryResponse(
            discovered_links=discovered_count,
            queued_tasks=queued_tasks,
            message=message
        )
        
    except Exception as e:
        logger.error(f"Discovery failed: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Discovery failed: {str(e)}")


@router.get("/sites/{domain}/config", response_model=SiteConfigResponse)
async def get_site_config(
    domain: str,
    db: AsyncSession = Depends(get_db)
):
    """
    Retrieve site-specific extraction configuration.
    
    Args:
        domain: Domain name
        db: Database session
        
    Returns:
        SiteConfigResponse: Site configuration
    """
    try:
        result = await db.execute(
            select(SiteConfig).where(SiteConfig.domain == domain.lower())
        )
        site_config = result.scalar_one_or_none()
        
        if not site_config:
            raise HTTPException(status_code=404, detail=f"Configuration not found for domain: {domain}")
        
        return SiteConfigResponse.from_orm(site_config)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get site config for {domain}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve site configuration")


@router.put("/sites/{domain}/config", response_model=SiteConfigResponse)
async def update_site_config(
    domain: str,
    config_data: dict,
    db: AsyncSession = Depends(get_db)
):
    """
    Update site-specific extraction configuration.
    
    Args:
        domain: Domain name
        config_data: Configuration data
        db: Database session
        
    Returns:
        SiteConfigResponse: Updated site configuration
    """
    try:
        domain = domain.lower()
        
        # Check if config exists
        result = await db.execute(
            select(SiteConfig).where(SiteConfig.domain == domain)
        )
        site_config = result.scalar_one_or_none()
        
        if site_config:
            # Update existing config
            site_config.config = config_data
        else:
            # Create new config
            site_config = SiteConfig(domain=domain, config=config_data)
            db.add(site_config)
        
        await db.commit()
        await db.refresh(site_config)
        
        logger.info(f"Updated site config for domain: {domain}")
        return SiteConfigResponse.from_orm(site_config)
        
    except Exception as e:
        logger.error(f"Failed to update site config for {domain}: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="Failed to update site configuration")


@router.get("/links", response_model=List[LinkResponse])
async def list_links(
    domain: Optional[str] = Query(None, description="Filter by domain"),
    status: Optional[LinkStatus] = Query(None, description="Filter by status"),
    limit: int = Query(100, ge=1, le=1000, description="Number of links to return"),
    offset: int = Query(0, ge=0, description="Number of links to skip"),
    db: AsyncSession = Depends(get_db)
):
    """
    List discovered links with filtering and pagination.
    
    Args:
        domain: Optional domain filter
        status: Optional status filter
        limit: Maximum number of results
        offset: Number of results to skip
        db: Database session
        
    Returns:
        List[LinkResponse]: List of links
    """
    try:
        query = select(Link)
        
        # Apply filters
        filters = []
        if domain:
            filters.append(Link.domain == domain.lower())
        if status:
            filters.append(Link.status == status)
        
        if filters:
            query = query.where(and_(*filters))
        
        # Apply pagination and ordering
        query = query.order_by(Link.priority.desc(), Link.created_at.desc())
        query = query.offset(offset).limit(limit)
        
        result = await db.execute(query)
        links = result.scalars().all()
        
        return [LinkResponse.from_orm(link) for link in links]
        
    except Exception as e:
        logger.error(f"Failed to list links: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve links")
