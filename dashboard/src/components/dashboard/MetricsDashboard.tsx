import React, { useState, useEffect } from 'react';
import {
  <PERSON>rid,
  <PERSON>,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Card,
  CardContent,
  Button,
  ButtonGroup,
} from '@mui/material';
import {
  Refresh,
  Timeline,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>Icon,
  <PERSON><PERSON><PERSON> as Bar<PERSON>hartIcon,
} from '@mui/icons-material';
import TimeSeriesChart from '../charts/TimeSeriesChart';
import <PERSON><PERSON>hart from '../charts/PieChart';
import <PERSON><PERSON><PERSON> from '../charts/BarChart';
import { useLinks } from '../../hooks/useApi';
import StatisticsService from '../../services/statistics';
import type { TimeSeriesData, ChartDataPoint, DomainStats } from '../../types/dashboard';

type TimeRange = '1h' | '6h' | '24h' | '7d';

const MetricsDashboard: React.FC = () => {
  const [timeRange, setTimeRange] = useState<TimeRange>('24h');
  const [refreshK<PERSON>, setRefresh<PERSON>ey] = useState(0);
  const { data: links, loading, error, refetch } = useLinks();

  const [timeSeriesData, setTimeSeriesData] = useState<TimeSeriesData[]>([]);
  const [statusDistribution, setStatusDistribution] = useState<ChartDataPoint[]>([]);
  const [domainStats, setDomainStats] = useState<DomainStats[]>([]);

  // Generate chart data when links data changes
  useEffect(() => {
    if (links) {
      // Generate time series data
      const hours =
        timeRange === '1h' ? 1 : timeRange === '6h' ? 6 : timeRange === '24h' ? 24 : 168;
      const timeSeries = StatisticsService.generateTimeSeriesData(links, hours);
      setTimeSeriesData(timeSeries);

      // Generate status distribution
      const statusDist = StatisticsService.generateStatusDistribution(links);
      setStatusDistribution(statusDist);

      // Generate domain statistics
      const domainData = StatisticsService.calculateDomainStats(links);
      setDomainStats(domainData.slice(0, 10)); // Top 10 domains
    }
  }, [links, timeRange]);

  const handleRefresh = () => {
    refetch();
    setRefreshKey(prev => prev + 1);
  };

  const handleTimeRangeChange = (newTimeRange: TimeRange) => {
    setTimeRange(newTimeRange);
  };

  // Transform domain stats for bar chart
  const domainChartData = domainStats.map(domain => ({
    name: domain.domain.length > 15 ? `${domain.domain.substring(0, 15)}...` : domain.domain,
    totalLinks: domain.totalLinks,
    completedLinks: domain.completedLinks,
    failedLinks: domain.failedLinks,
    successRate: domain.successRate,
  }));

  return (
    <Box>
      {/* Header with controls */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" component="h2">
          Analytics & Metrics
        </Typography>

        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          {/* Time Range Selector */}
          <ButtonGroup variant="outlined" size="small">
            {(['1h', '6h', '24h', '7d'] as TimeRange[]).map(range => (
              <Button
                key={range}
                variant={timeRange === range ? 'contained' : 'outlined'}
                onClick={() => handleTimeRangeChange(range)}
              >
                {range}
              </Button>
            ))}
          </ButtonGroup>

          {/* Refresh Button */}
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={handleRefresh}
            disabled={loading}
          >
            Refresh
          </Button>
        </Box>
      </Box>

      {error && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography color="error">Error loading data: {error}</Typography>
          </CardContent>
        </Card>
      )}

      <Grid container spacing={3}>
        {/* Time Series Chart - Link Discovery Over Time */}
        <Grid item xs={12} lg={8}>
          <TimeSeriesChart
            title="Link Discovery Over Time"
            data={timeSeriesData}
            height={350}
            type="area"
            yAxisLabel="Links Discovered"
            loading={loading}
            key={`timeseries-${refreshKey}`}
          />
        </Grid>

        {/* Status Distribution Pie Chart */}
        <Grid item xs={12} lg={4}>
          <PieChart
            title="Link Status Distribution"
            data={statusDistribution}
            height={350}
            loading={loading}
            key={`pie-${refreshKey}`}
          />
        </Grid>

        {/* Domain Statistics Bar Chart */}
        <Grid item xs={12}>
          <BarChart
            title="Top Domains by Link Count"
            data={domainChartData}
            height={400}
            bars={[
              { key: 'totalLinks', name: 'Total Links', color: '#1976d2' },
              { key: 'completedLinks', name: 'Completed', color: '#4caf50' },
              { key: 'failedLinks', name: 'Failed', color: '#f44336' },
            ]}
            yAxisLabel="Number of Links"
            loading={loading}
            key={`bar-${refreshKey}`}
          />
        </Grid>

        {/* Success Rate by Domain */}
        <Grid item xs={12} md={6}>
          <BarChart
            title="Success Rate by Domain"
            data={domainChartData}
            height={300}
            bars={[{ key: 'successRate', name: 'Success Rate (%)', color: '#4caf50' }]}
            yAxisLabel="Success Rate (%)"
            loading={loading}
            key={`success-${refreshKey}`}
          />
        </Grid>

        {/* Processing Summary */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                <Timeline color="primary" />
                <Typography variant="h6">Processing Summary</Typography>
              </Box>

              {links && (
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography variant="h4" color="primary">
                        {links.length}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Total Links
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography variant="h4" color="success.main">
                        {links.filter(l => l.status === 'completed').length}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Processed
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography variant="h4" color="warning.main">
                        {links.filter(l => l.status === 'pending').length}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Pending
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography variant="h4" color="error.main">
                        {links.filter(l => l.status === 'failed').length}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Failed
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default MetricsDashboard;
